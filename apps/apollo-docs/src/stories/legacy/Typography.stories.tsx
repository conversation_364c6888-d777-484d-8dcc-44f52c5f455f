import { createTheme, ThemeProvider, Typo<PERSON> } from "@apollo/ui/legacy"
import type { <PERSON>a, StoryObj } from "@storybook/react"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Data Display/Typography",
  component: Typography,
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Typography provides consistent text styling across the application. It supports different semantic levels, alignment options, colors, and decorators for various text use cases.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    level: {
      control: { type: "select" },
      options: ["h1", "h2", "h3", "h4", "h5", "body-1", "body-2", "caption"],
      description: "Typography level/variant",
    },
    align: {
      control: { type: "select" },
      options: ["left", "center", "right", "justify", "inherit"],
      description: "Text alignment",
    },
    color: {
      control: { type: "select" },
      options: ["primary", "danger"],
      description: "Text color variant",
    },
    gutterBottom: {
      control: { type: "boolean" },
      description: "Add bottom margin",
    },
    noWrap: {
      control: { type: "boolean" },
      description: "Prevent text wrapping",
    },
    children: {
      control: { type: "text" },
      description: "Text content",
    },
  },
} satisfies Meta<typeof Typography>

export default meta
type Story = StoryObj<typeof meta>

// Basic typography
export const Basic: Story = {
  args: {
    children: "This is basic typography text.",
    level: "body-1",
  },
  parameters: {
    docs: {
      description: {
        story: "Basic typography with default body-1 level.",
      },
    },
  },
}

// All typography levels
export const TypographyLevels: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "600px",
      }}
    >
      <Typography level="h1">Heading 1 - Main page title</Typography>
      <Typography level="h2">Heading 2 - Section title</Typography>
      <Typography level="h3">Heading 3 - Subsection title</Typography>
      <Typography level="h4">Heading 4 - Component title</Typography>
      <Typography level="h5">Heading 5 - Small heading</Typography>
      <Typography level="body-1">
        Body 1 - Regular paragraph text with normal font weight
      </Typography>
      <Typography level="body-2">
        Body 2 - Secondary text content with lighter appearance
      </Typography>
      <Typography level="caption">
        Caption - Small text for labels, hints, and metadata
      </Typography>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "All available typography levels from h1 to caption with example usage.",
      },
    },
  },
}

// Text alignment options
export const TextAlignment: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "500px",
      }}
    >
      <div>
        <Typography level="h4" gutterBottom>
          Text Alignment Options
        </Typography>
      </div>

      <div style={{ border: "1px dashed #ddd", padding: "12px" }}>
        <Typography align="left">Left aligned text (default)</Typography>
      </div>

      <div style={{ border: "1px dashed #ddd", padding: "12px" }}>
        <Typography align="center">Center aligned text</Typography>
      </div>

      <div style={{ border: "1px dashed #ddd", padding: "12px" }}>
        <Typography align="right">Right aligned text</Typography>
      </div>

      <div style={{ border: "1px dashed #ddd", padding: "12px" }}>
        <Typography align="justify">
          Justified text that spreads content evenly across the full width of
          the container, creating clean edges on both sides. This is
          particularly useful for longer paragraphs in formal documents or
          articles.
        </Typography>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Different text alignment options: left, center, right, and justify.",
      },
    },
  },
}

// Color variants
export const ColorVariants: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        width: "400px",
      }}
    >
      <Typography level="h4" gutterBottom>
        Color Variants
      </Typography>

      <Typography>Default text color (inherits from theme)</Typography>
      <Typography color="primary">Primary color text for emphasis</Typography>
      <Typography color="danger">
        Danger color text for warnings and errors
      </Typography>

      <div style={{ marginTop: "16px" }}>
        <Typography level="h5" gutterBottom>
          In Different Levels:
        </Typography>
        <Typography level="h3" color="primary">
          Primary Heading
        </Typography>
        <Typography level="body-1" color="danger">
          Danger body text
        </Typography>
        <Typography level="caption" color="primary">
          Primary caption text
        </Typography>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Typography with different color variants: default, primary, and danger.",
      },
    },
  },
}

// Gutter bottom and spacing
export const SpacingAndLayout: Story = {
  render: () => (
    <div style={{ width: "500px" }}>
      <div style={{ marginBottom: "32px" }}>
        <Typography level="h4" gutterBottom>
          With Gutter Bottom
        </Typography>
        <Typography level="h5" gutterBottom>
          This heading has margin bottom
        </Typography>
        <Typography level="body-1" gutterBottom>
          This paragraph has margin bottom
        </Typography>
        <Typography level="body-1">
          This paragraph follows without extra spacing
        </Typography>
      </div>

      <div style={{ marginBottom: "32px" }}>
        <Typography level="h4" gutterBottom>
          Without Gutter Bottom
        </Typography>
        <Typography level="h5">This heading has no margin bottom</Typography>
        <Typography level="body-1">
          This paragraph has no margin bottom
        </Typography>
        <Typography level="body-1">All text is tightly spaced</Typography>
      </div>

      <div>
        <Typography level="h4" gutterBottom>
          Mixed Spacing
        </Typography>
        <Typography level="body-1" gutterBottom>
          This is the first paragraph with gutter bottom spacing.
        </Typography>
        <Typography level="body-1">
          This is the second paragraph without gutter bottom.
        </Typography>
        <Typography level="body-1" gutterBottom>
          This is the third paragraph with gutter bottom again.
        </Typography>
        <Typography level="caption">
          This caption text follows with tight spacing.
        </Typography>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Typography spacing control with gutterBottom prop for consistent vertical rhythm.",
      },
    },
  },
}

// No wrap and overflow handling
export const TextOverflow: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        width: "400px",
      }}
    >
      <Typography level="h4" gutterBottom>
        Text Overflow Handling
      </Typography>

      <div>
        <Typography level="h5" gutterBottom>
          Normal wrapping (default):
        </Typography>
        <div
          style={{ border: "1px solid #ddd", padding: "12px", width: "250px" }}
        >
          <Typography level="body-1">
            This is a very long text that will wrap to multiple lines when the
            container width is limited
          </Typography>
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          No wrap with ellipsis:
        </Typography>
        <div
          style={{ border: "1px solid #ddd", padding: "12px", width: "250px" }}
        >
          <Typography level="body-1" noWrap>
            This is a very long text that will be truncated with ellipsis when
            noWrap is enabled
          </Typography>
        </div>
      </div>

      <div>
        <Typography level="h5" gutterBottom>
          Caption with noWrap:
        </Typography>
        <div
          style={{ border: "1px solid #ddd", padding: "12px", width: "200px" }}
        >
          <Typography level="caption" noWrap>
            Very long caption text that gets truncated
          </Typography>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Text overflow handling with noWrap prop for single-line text with ellipsis.",
      },
    },
  },
}

// Typography in components
export const InComponents: Story = {
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "24px",
        width: "600px",
      }}
    >
      {/* Card component example */}
      <div
        style={{
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          padding: "20px",
          background: "#fff",
        }}
      >
        <Typography level="h4" gutterBottom>
          Product Card
        </Typography>
        <Typography level="h5" color="primary" gutterBottom>
          Premium Wireless Headphones
        </Typography>
        <Typography level="body-1" gutterBottom>
          High-quality wireless headphones with noise cancellation and premium
          sound quality. Perfect for music lovers and professionals.
        </Typography>
        <Typography level="body-2" style={{ color: "#666" }} gutterBottom>
          SKU: WH-1000XM4 | In Stock: 15 units
        </Typography>
        <Typography level="h5" color="primary">
          $299.99
        </Typography>
      </div>

      {/* Alert component example */}
      <div
        style={{
          border: "1px solid #ffc107",
          borderRadius: "6px",
          padding: "16px",
          background: "#fff3cd",
        }}
      >
        <Typography level="h5" startDecorator={<span>⚠️</span>} gutterBottom>
          Important Notice
        </Typography>
        <Typography level="body-1" gutterBottom>
          Your subscription will expire in 7 days. Please renew to continue
          using premium features.
        </Typography>
        <Typography level="caption" style={{ color: "#856404" }}>
          Renewal can be done from your account settings.
        </Typography>
      </div>

      {/* Article excerpt example */}
      <div>
        <Typography level="h3" gutterBottom>
          Getting Started with Design Systems
        </Typography>
        <Typography level="caption" style={{ color: "#666" }} gutterBottom>
          Published on March 15, 2024 • 5 min read
        </Typography>
        <Typography level="body-1" gutterBottom>
          Design systems have become essential tools for modern product
          development. They provide a shared language between designers and
          developers, ensuring consistency across all touchpoints of a digital
          experience.
        </Typography>
        <Typography level="body-2" style={{ color: "#666" }}>
          In this article, we'll explore the fundamental concepts of design
          systems and how to implement them effectively in your organization...
        </Typography>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Typography used in real component contexts: product cards, alerts, and article layouts.",
      },
    },
  },
}

// Typography hierarchy
export const TypographyHierarchy: Story = {
  render: () => (
    <div style={{ width: "700px", lineHeight: "1.6" }}>
      <Typography level="h1" gutterBottom>
        Design System Documentation
      </Typography>

      <Typography level="body-1" gutterBottom>
        This document outlines the typography system used throughout our design
        system. Typography creates hierarchy and guides users through content
        effectively.
      </Typography>

      <Typography level="h2" gutterBottom>
        Getting Started
      </Typography>

      <Typography level="body-1" gutterBottom>
        Our typography system is built with accessibility and readability in
        mind. Each level serves a specific purpose in the content hierarchy.
      </Typography>

      <Typography level="h3" gutterBottom>
        Heading Levels
      </Typography>

      <Typography level="body-1" gutterBottom>
        Use heading levels to create clear content structure:
      </Typography>

      <Typography level="h4" gutterBottom>
        Primary Headings (H1)
      </Typography>
      <Typography level="body-2" gutterBottom>
        Used for main page titles and the most important content sections.
      </Typography>

      <Typography level="h4" gutterBottom>
        Secondary Headings (H2-H3)
      </Typography>
      <Typography level="body-2" gutterBottom>
        Used for major sections and subsections within a page.
      </Typography>

      <Typography level="h4" gutterBottom>
        Component Headings (H4-H5)
      </Typography>
      <Typography level="body-2" gutterBottom>
        Used for component titles and smaller content groups.
      </Typography>

      <Typography level="h3" gutterBottom>
        Body Text
      </Typography>

      <Typography level="body-1" gutterBottom>
        Body-1 is used for primary content and should be highly readable. It's
        the default choice for most paragraph text.
      </Typography>

      <Typography level="body-2" gutterBottom>
        Body-2 is used for secondary content, descriptions, and supporting text.
        It's slightly smaller and less prominent than Body-1.
      </Typography>

      <Typography level="caption" style={{ color: "#666" }}>
        Caption text is used for metadata, labels, and small descriptive text.
        It should be used sparingly and only for non-essential information.
      </Typography>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Complete typography hierarchy example showing proper usage in a documentation context.",
      },
    },
  },
}
