import { useState } from "react"
import { createThemeV2, Tabs, Theme } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

const meta: Meta<typeof Tabs.Root> = {
  title: "@apollo∕ui/Components/Layout/Tabs",
  component: Tabs.Root,
  subcomponents: {
    Tab: Tabs.Tab,
    TabList: Tabs.List,
    TabPanel: Tabs.Panel,
    Indicator: Tabs.Indicator,
  },
  decorators: [
    (Story) => (
      <Theme theme={createThemeV2()}>
        <Story />
      </Theme>
    ),
  ],
  tags: ["autodocs"],
  parameters: {
    docs: {
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <h3>Import</h3>
          <Source code={`import { Tabs } from "@apollo/ui"`} language="tsx" />

          <Primary />

          <h3>Props</h3>
          <h4>Tabs.Root</h4>
          <ArgTypes of={Tabs.Root} />

          <h4>Tabs.Tab</h4>
          <ArgTypes of={Tabs.Tab} />

          <h4>Tabs.List</h4>
          <ArgTypes of={Tabs.List} />

          <h4>Tabs.Indicator</h4>
          <ArgTypes of={Tabs.Indicator} />

          <h4>Tabs.Panel</h4>
          <ArgTypes of={Tabs.Panel} />
        </>
      ),
    },
  },
}
export default meta

type Story = StoryObj<typeof Tabs.Root>

export const Basic: Story = {
  render: () => (
    <Tabs.Root defaultValue="tab1">
      <Tabs.List>
        <Tabs.Tab value="tab1">Tab One</Tabs.Tab>
        <Tabs.Tab value="tab2">Tab Two</Tabs.Tab>
        <Tabs.Tab value="tab3">Tab Three</Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value="tab1">Content for Tab One</Tabs.Panel>
      <Tabs.Panel value="tab2">Content for Tab Two</Tabs.Panel>
      <Tabs.Panel value="tab3">Content for Tab Three</Tabs.Panel>
    </Tabs.Root>
  ),
}

export const FullWidth: Story = {
  render: () => (
    <Tabs.Root defaultValue="tab1" fullWidth>
      <Tabs.List>
        <Tabs.Tab value="tab1">Tab One</Tabs.Tab>
        <Tabs.Tab value="tab2">Tab Two</Tabs.Tab>
        <Tabs.Tab value="tab3">Tab Three</Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value="tab1">Content for Tab One</Tabs.Panel>
      <Tabs.Panel value="tab2">Content for Tab Two</Tabs.Panel>
      <Tabs.Panel value="tab3">Content for Tab Three</Tabs.Panel>
    </Tabs.Root>
  ),
}

export const FitContent: Story = {
  render: () => (
    <Tabs.Root defaultValue="tab1">
      <Tabs.List>
        <Tabs.Tab value="tab1" fitContent>
          Short
        </Tabs.Tab>
        <Tabs.Tab value="tab2" fitContent>
          Medium Tab
        </Tabs.Tab>
        <Tabs.Tab value="tab3" fitContent>
          Very Long Tab Label
        </Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value="tab1">Short content</Tabs.Panel>
      <Tabs.Panel value="tab2">Medium content</Tabs.Panel>
      <Tabs.Panel value="tab3">Long content</Tabs.Panel>
    </Tabs.Root>
  ),
}

export const WithIcons: Story = {
  render: () => (
    <Tabs.Root defaultValue="tab1">
      <Tabs.List>
        <Tabs.Tab value="tab1">
          <span role="img" aria-label="home">
            🏠
          </span>{" "}
          Home
        </Tabs.Tab>
        <Tabs.Tab value="tab2">
          <span role="img" aria-label="settings">
            ⚙️
          </span>{" "}
          Settings
        </Tabs.Tab>
        <Tabs.Tab value="tab3">
          <span role="img" aria-label="profile">
            👤
          </span>{" "}
          Profile
        </Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value="tab1">Home content</Tabs.Panel>
      <Tabs.Panel value="tab2">Settings content</Tabs.Panel>
      <Tabs.Panel value="tab3">Profile content</Tabs.Panel>
    </Tabs.Root>
  ),
}

export const Disabled: Story = {
  render: () => (
    <Tabs.Root defaultValue="tab1">
      <Tabs.List>
        <Tabs.Tab value="tab1">Active Tab</Tabs.Tab>
        <Tabs.Tab value="tab2" disabled>
          Disabled Tab
        </Tabs.Tab>
        <Tabs.Tab value="tab3">Another Tab</Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>

      <Tabs.Panel value="tab1">Content for Active Tab</Tabs.Panel>
      <Tabs.Panel value="tab2">This should not be visible</Tabs.Panel>
      <Tabs.Panel value="tab3">Content for Another Tab</Tabs.Panel>
    </Tabs.Root>
  ),
}

export const Controlled: Story = {
  render: () => {
    const [value, setValue] = useState("tab1")
    return (
      <Tabs.Root defaultValue={value} onValueChange={setValue}>
        <Tabs.List>
          <Tabs.Tab value="tab1">Controlled One</Tabs.Tab>
          <Tabs.Tab value="tab2">Controlled Two</Tabs.Tab>
          <Tabs.Tab value="tab3">Controlled Three</Tabs.Tab>
          <Tabs.Indicator />
        </Tabs.List>
        <Tabs.Panel value="tab1">Panel One</Tabs.Panel>
        <Tabs.Panel value="tab2">Panel Two</Tabs.Panel>
        <Tabs.Panel value="tab3">Panel Three</Tabs.Panel>
      </Tabs.Root>
    )
  },
}
