import { useState } from "react"
import { <PERSON><PERSON>rdion, <PERSON><PERSON>, createThemeV2, Theme, Typography } from "@apollo/ui"
import type { <PERSON>a, StoryObj } from "@storybook/react"

// Metadata for the Storybook component
const meta: Meta<typeof Accordion> = {
  title: "@apollo∕ui/Components/Data Display/Accordion",
  component: Accordion,
  decorators: [
    (Story) => (
      <Theme theme={createThemeV2()}>
        <Story />
      </Theme>
    ),
  ],
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    label: {
      control: "text",
      description: "Content for the accordion header.",
    },
    open: {
      control: "boolean",
      description: "Controls the open state when component is controlled.",
    },
    defaultOpen: {
      control: "boolean",
      description: "Initial open state for uncontrolled component.",
    },
    onOpenChange: {
      action: "onOpenChange",
      description: "Callback when open state changes.",
    },
    iconPosition: {
      control: { type: "radio" },
      options: ["start", "end", "both"],
      description: "Position of the chevron icon.",
    },
    borderless: {
      control: "boolean",
      description: "Removes the border styling.",
    },
    variant: {
      control: { type: "radio" },
      options: ["default", "danger"],
      description: "Visual variant of the accordion.",
    },
    iconVariant: {
      control: { type: "radio" },
      options: ["default", "primary"],
      description: "Visual variant of the icon.",
    },
    fullWidth: {
      control: "boolean",
      description: "Whether accordion takes full width of container.",
    },
    keepMounted: {
      control: "boolean",
      description: "Keep contents mounted when closed.",
    },
    disabled: {
      control: "boolean",
      description: "Disables the accordion.",
    },
    hasDivider: {
      control: "boolean",
      description: "Adds a divider line below the accordion header.",
    },
    className: {
      control: "text",
      description: "Additional CSS class names for the root element.",
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Basic Accordion Story
export const Basic: Story = {
  args: {
    label: "Basic Accordion",
    children: (
      <Typography level="bodyLarge">
        This is the content of the basic accordion.
      </Typography>
    ),
    defaultOpen: false,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Dnager Variant Story
export const Danger: Story = {
  args: {
    label: "Error Variant",
    children: (
      <Typography level="bodyLarge">
        This is the content of the accordion.
      </Typography>
    ),
    variant: "danger",
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Icon Positioning Story
export const IconPositioning: Story = {
  args: {
    label: "Icon Positioning",
    children: (
      <Typography level="bodyLarge">
        This is the content of the accordion.
      </Typography>
    ),
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args} iconPosition="start">
        {args.children}
      </Accordion>
    </div>
  ),
}

// Has Divider Accordion Story
export const WithDivider: Story = {
  args: {
    label: "With Divider",
    children: (
      <Typography level="bodyLarge">
        This is the content of the accordion. It is hidden by default and can be
        toggled by clicking the header.
      </Typography>
    ),
    hasDivider: true,
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px"}}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Disabled Accordion Story
export const Disabled: Story = {
  args: {
    label: "Disabled Accordion",
    children: (
      <Typography level="bodyLarge">
        You should not be able to see this content because the accordion is
        disabled.
      </Typography>
    ),
    disabled: true,
    fullWidth: true,
  },
  render: (args) => (
    <div style={{ width: "600px"}}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}

// Controlled Accordion Story
export const Controlled: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(false)
    return (
      <div style={{ width: "600px" }}>
        <Button
          onClick={() => setIsOpen(!isOpen)}
          style={{ marginBottom: "1rem", padding: "0.5rem 1rem" }}
        >
          Toggle Accordion
        </Button>
        <Accordion {...args} open={isOpen} onOpenChange={setIsOpen}>
          {args.children}
        </Accordion>
      </div>
    )
  },
  args: {
    label: "Controlled Accordion",
    children: (
      <Typography level="bodyLarge">
        This accordion's state is controlled by the button above. The
        `onOpenChange` callback updates the external state.
      </Typography>
    ),
    fullWidth: true,
  },
}

export const Complex: Story = {
  args: {
    label: (
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography level="labelMedium" style={{ maxWidth: "100px" }}>
          011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
        </Typography>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-end",
            gap: "8px",
          }}
        >
          <Typography level="labelMedium">เปิดทำการ</Typography>
          <Button
            onClick={(e) => e.stopPropagation()}
            variant="outline"
            size="small"
          >
            เพิ่ม
          </Button>
        </div>
      </div>
    ),
    children: (
      <div style={{ display: "flex", flexDirection: "column" }}>
        <span>Model: CJ SUPERMARKET + BAO CAFÉ&WASH </span>
        <span>Open date: 30/08/2004 Close</span>
        <span>date: 30/08/2004</span>
      </div>
    ),
    defaultOpen: true,
    borderless: true,
    fullWidth: true,
    iconPosition: "start",
    iconVariant: "primary",
  },
   render: (args) => (
    <div style={{ width: "600px" }}>
      <Accordion {...args}>{args.children}</Accordion>
    </div>
  ),
}
