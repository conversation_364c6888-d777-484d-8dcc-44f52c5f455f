import { useState } from "react"
import {
    Apollo<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  createThemeV2,
  ToastProvider,
  useToast,
} from "@apollo/ui"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

import "@apollo/ui/style.css"

const meta: Meta = {
  title: "@apollo∕ui/Components/Feedback/Toast",
  decorators: [
    (Story) => (
      <ApolloProvider themeProps={{ theme: createThemeV2() }}>
          <Story />
      </ApolloProvider>
    ),
  ],
  tags: ["autodocs"],
  parameters: {
    docs: {
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <h3>Import</h3>
          <Source
            code={`import { useToast, ToastProvider } from "@apollo/ui"`}
            language="tsx"
          />
          <Primary />
          <h3>Hook API</h3>
          <p>The useToast hook provides the following methods:</p>
          <ul>
            <li><code>add(options)</code> - Add a new toast</li>
            <li><code>update(toastId, options)</code> - Update an existing toast</li>
            <li><code>close(toastId)</code> - Close a specific toast</li>
            <li><code>toasts</code> - Array of current toasts</li>
          </ul>
          <Stories />
        </>
      ),
    },
  },
}

export default meta
type Story = StoryObj

// Basic usage with different types
export const BasicUsage: Story = {
  render: () => {
    const BasicToastExample = () => {
      const { add } = useToast()

      const showSuccess = () => {
        add({
          title: "Success!",
          description: "Your action was completed successfully.",
          type: "success",
        })
      }

      const showError = () => {
        add({
          title: "Error occurred",
          description: "Something went wrong. Please try again.",
          type: "error",
        })
      }

      const showWarning = () => {
        add({
          title: "Warning",
          description: "Please review your input before proceeding.",
          type: "warning",
        })
      }

      const showInfo = () => {
        add({
          title: "Information",
          description: "Here's some helpful information for you.",
          type: "info",
        })
      }

      return (
        <div style={{ display: "flex", gap: "12px", flexWrap: "wrap" }}>
          <Button onClick={showSuccess} variant="solid">
            Success Toast
          </Button>
          <Button onClick={showError} variant="solid">
            Error Toast
          </Button>
          <Button onClick={showWarning} variant="solid">
            Warning Toast
          </Button>
          <Button onClick={showInfo} variant="solid">
            Info Toast
          </Button>
        </div>
      )
    }

    return <BasicToastExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Basic toast usage with different types: success, error, warning, and info.",
      },
    },
  },
}

// Position: Top Left
export const PositionTopLeft: Story = {
  render: () => {
    const TopLeftExample = () => {
      const { add } = useToast()

      const showTopLeftToast = () => {
        add({
          title: "Top Left Position",
          description: "This toast appears at the top-left corner of the screen.",
          type: "info",
          position: "top-left",
          timeout: 3000,
        })
      }

      return (
        <Button onClick={showTopLeftToast} variant="outline">
          Show Top Left Toast
        </Button>
      )
    }

    return <TopLeftExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the top-left corner of the screen.",
      },
    },
  },
}

// Position: Top Center
export const PositionTopCenter: Story = {
  render: () => {
    const TopCenterExample = () => {
      const { add } = useToast()

      const showTopCenterToast = () => {
        add({
          title: "Top Center Position",
          description: "This toast appears at the top-center of the screen.",
          type: "success",
          position: "top-center",
          timeout: 3000,
        })
      }

      return (
        <Button onClick={showTopCenterToast} variant="outline">
          Show Top Center Toast
        </Button>
      )
    }

    return <TopCenterExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the top-center of the screen.",
      },
    },
  },
}

// Position: Top Right
export const PositionTopRight: Story = {
  render: () => {
    const TopRightExample = () => {
      const { add } = useToast()

      const showTopRightToast = () => {
        add({
          title: "Top Right Position",
          description: "This toast appears at the top-right corner of the screen.",
          type: "warning",
          position: "top-right",
          timeout: 3000,
        })
      }

      return (
        <Button onClick={showTopRightToast} variant="outline">
          Show Top Right Toast
        </Button>
      )
    }

    return <TopRightExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the top-right corner of the screen.",
      },
    },
  },
}

// Position: Bottom Left
export const PositionBottomLeft: Story = {
  render: () => {
    const BottomLeftExample = () => {
      const { add } = useToast()

      const showBottomLeftToast = () => {
        add({
          title: "Bottom Left Position",
          description: "This toast appears at the bottom-left corner of the screen.",
          type: "error",
          position: "bottom-left",
          timeout: 3000,
        })
      }

      return (
        <Button onClick={showBottomLeftToast} variant="outline">
          Show Bottom Left Toast
        </Button>
      )
    }

    return <BottomLeftExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the bottom-left corner of the screen.",
      },
    },
  },
}

// Position: Bottom Center
export const PositionBottomCenter: Story = {
  render: () => {
    const BottomCenterExample = () => {
      const { add } = useToast()

      const showBottomCenterToast = () => {
        add({
          title: "Bottom Center Position",
          description: "This toast appears at the bottom-center of the screen.",
          type: "info",
          position: "bottom-center",
          timeout: 3000,
        })
      }

      return (
        <Button onClick={showBottomCenterToast} variant="outline">
          Show Bottom Center Toast
        </Button>
      )
    }

    return <BottomCenterExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the bottom-center of the screen.",
      },
    },
  },
}

// Position: Bottom Right
export const PositionBottomRight: Story = {
  render: () => {
    const BottomRightExample = () => {
      const { add } = useToast()

      const showBottomRightToast = () => {
        add({
          title: "Bottom Right Position",
          description: "This toast appears at the bottom-right corner of the screen.",
          type: "success",
          position: "bottom-right",
          timeout: 3000,
        })
      }

      return (
        <Button onClick={showBottomRightToast} variant="outline">
          Show Bottom Right Toast
        </Button>
      )
    }

    return <BottomRightExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast positioned at the bottom-right corner of the screen.",
      },
    },
  },
}

// Toast with custom content and decorators
export const WithDecorators: Story = {
  render: () => {
    const DecoratorExample = () => {
      const { add } = useToast()

      const showWithStartDecorator = () => {
        add({
          title: "File uploaded",
          description: "Your document has been successfully uploaded.",
          type: "success",
          startDecorator: <span>📁</span>,
        })
      }

      const showWithEndDecorator = () => {
        add({
          title: "Action required",
          description: "Please review the changes before proceeding.",
          type: "warning",
          endDecorator: (
            <Button variant="outline" size="small">
              Review
            </Button>
          ),
        })
      }

      const showWithAction = () => {
        add({
          title: "Update available",
          description: "A new version is available for download.",
          type: "info",
          action: (
            <Button variant="solid" size="small">
              Update Now
            </Button>
          ),
        })
      }

      return (
        <div style={{ display: "flex", gap: "12px", flexDirection: "column", maxWidth: "300px" }}>
          <Button onClick={showWithStartDecorator} variant="outline">
            With Start Decorator
          </Button>
          <Button onClick={showWithEndDecorator} variant="outline">
            With End Decorator
          </Button>
          <Button onClick={showWithAction} variant="outline">
            With Action Button
          </Button>
        </div>
      )
    }

    return <DecoratorExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast can include decorators and action buttons for enhanced functionality.",
      },
    },
  },
}

// Toast management - update and close
export const ToastManagement: Story = {
  render: () => {
    const ManagementExample = () => {
      const { add, update, close, toasts } = useToast()
      const [lastToastId, setLastToastId] = useState<string | null>(null)

      const addProgressToast = () => {
        const toastId = `progress-${Date.now()}`
        add({
          id: toastId,
          title: "Processing...",
          description: "Your request is being processed.",
          type: "info",
        })
        setLastToastId(toastId)
      }

      const updateToSuccess = () => {
        if (lastToastId) {
          update(lastToastId, {
            title: "Completed!",
            description: "Your request has been processed successfully.",
            type: "success",
          })
        }
      }

      const updateToError = () => {
        if (lastToastId) {
          update(lastToastId, {
            title: "Failed",
            description: "An error occurred while processing your request.",
            type: "error",
          })
        }
      }

      const closeLastToast = () => {
        if (lastToastId) {
          close(lastToastId)
          setLastToastId(null)
        }
      }

      const closeAllToasts = () => {
        toasts.forEach((toast) => close(toast.id))
        setLastToastId(null)
      }

      return (
        <div style={{ display: "flex", gap: "12px", flexDirection: "column", maxWidth: "300px" }}>
          <Button onClick={addProgressToast} variant="solid">
            Add Progress Toast
          </Button>
          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              onClick={updateToSuccess}
              variant="outline"
              size="small"
              disabled={!lastToastId}
            >
              Update to Success
            </Button>
            <Button
              onClick={updateToError}
              variant="outline"
              size="small"
              disabled={!lastToastId}
            >
              Update to Error
            </Button>
          </div>
          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              onClick={closeLastToast}
              variant="outline"
              size="small"
              disabled={!lastToastId}
            >
              Close Last
            </Button>
            <Button
              onClick={closeAllToasts}
              variant="outline"
              size="small"
              disabled={toasts.length === 0}
            >
              Close All ({toasts.length})
            </Button>
          </div>
        </div>
      )
    }

    return <ManagementExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Demonstrates toast management capabilities: updating existing toasts and closing specific or all toasts.",
      },
    },
  },
}

// Multiple toasts and stacking
export const MultipleToasts: Story = {
  render: () => {
    const MultipleExample = () => {
      const { add } = useToast()

      const showMultipleToasts = () => {
        const types = ["success", "info", "warning", "error"] as const
        const messages = [
          { title: "Task 1 Complete", description: "First task finished successfully." },
          { title: "Processing Task 2", description: "Second task is in progress." },
          { title: "Warning for Task 3", description: "Third task needs attention." },
          { title: "Task 4 Failed", description: "Fourth task encountered an error." },
        ]

        messages.forEach((message, index) => {
          setTimeout(() => {
            add({
              ...message,
              type: types[index],
            })
          }, index * 500)
        })
      }

      const showSequentialToasts = () => {
        add({
          title: "Step 1",
          description: "Initializing process...",
          type: "info",
        })

        setTimeout(() => {
          add({
            title: "Step 2",
            description: "Processing data...",
            type: "info",
          })
        }, 1000)

        setTimeout(() => {
          add({
            title: "Complete",
            description: "All steps completed successfully!",
            type: "success",
          })
        }, 2000)
      }

      return (
        <div style={{ display: "flex", gap: "12px", flexDirection: "column", maxWidth: "300px" }}>
          <Button onClick={showMultipleToasts} variant="solid">
            Show Multiple Toasts
          </Button>
          <Button onClick={showSequentialToasts} variant="outline">
            Show Sequential Toasts
          </Button>
        </div>
      )
    }

    return <MultipleExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Demonstrates how multiple toasts stack and appear sequentially.",
      },
    },
  },
}

// Toast with custom close behavior
export const CustomCloseBehavior: Story = {
  render: () => {
    const CustomCloseExample = () => {
      const { add } = useToast()

      const showWithCustomClose = () => {
        add({
          title: "Custom Close Handler",
          description: "This toast has a custom close behavior.",
          type: "info",
          onClose: () => {
            console.log("Toast closed with custom handler!")
            // You can add custom logic here
          },
        })
      }

      const showPersistentToast = () => {
        add({
          title: "Important Notice",
          description: "This is an important message that requires attention.",
          type: "warning",
          // Note: The actual persistence behavior depends on the base implementation
          // This demonstrates the API structure
        })
      }

      return (
        <div style={{ display: "flex", gap: "12px", flexDirection: "column", maxWidth: "300px" }}>
          <Button onClick={showWithCustomClose} variant="outline">
            Toast with Custom Close
          </Button>
          <Button onClick={showPersistentToast} variant="outline">
            Important Notice
          </Button>
        </div>
      )
    }

    return <CustomCloseExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Toast with custom close behavior and event handlers.",
      },
    },
  },
}

// Toast with timeout configurations
export const WithTimeout: Story = {
  render: () => {
    const TimeoutExample = () => {
      const { add } = useToast()

      const showQuickToast = () => {
        add({
          title: "Quick notification",
          description: "This toast will disappear in 1 second.",
          type: "info",
          timeout: 1000,
        })
      }

      const showMediumToast = () => {
        add({
          title: "Medium duration",
          description: "This toast will disappear in 3 seconds.",
          type: "success",
          timeout: 3000,
        })
      }

      const showLongToast = () => {
        add({
          title: "Long notification",
          description: "This toast will stay for 8 seconds.",
          type: "warning",
          timeout: 8000,
        })
      }

      const showPersistentToast = () => {
        add({
          title: "Persistent toast",
          description: "This toast won't auto-dismiss. Click the X to close.",
          type: "error",
          timeout: 0, // 0 means no auto-dismiss
          onClose: () => {
            console.log("Persistent toast closed!")
          },
        })
      }


      return (
        <div style={{ display: "flex", gap: "12px", flexDirection: "column", maxWidth: "300px" }}>
          <Button onClick={showQuickToast} variant="outline">
            Quick (1s)
          </Button>
          <Button onClick={showMediumToast} variant="outline">
            Medium (3s)
          </Button>
          <Button onClick={showLongToast} variant="outline">
            Long (8s)
          </Button>
          <Button onClick={showPersistentToast} variant="outline">
            Persistent (No timeout)
          </Button>
        </div>
      )
    }

    return <TimeoutExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Demonstrates different timeout configurations for automatic toast dismissal. Set timeout to 0 for persistent toasts that don't auto-dismiss.",
      },
    },
  },
}

// Toast with isClosable property
export const IsClosable: Story = {
  render: () => {
    const IsClosableExample = () => {
      const { add } = useToast()

      const showClosableToast = () => {
        add({
          title: "Closable Toast",
          description: "This toast can be closed by clicking the X button.",
          type: "info",
          isClosable: true,
        })
      }

      const showNonClosableToast = () => {
        add({
          title: "Non-closable Toast",
          description: "This toast cannot be closed manually and will auto-dismiss.",
          type: "warning",
          isClosable: false,
          timeout: 5000,
        })
      }

      const showClosableWithCustomClose = () => {
        add({
          title: "Closable with Custom Handler",
          description: "This toast has both close button and custom close logic.",
          type: "success",
          onClose: () => {
            console.log("Custom close handler executed!")
            alert("Toast was closed!")
          },
        })
      }

      return (
        <div style={{ display: "flex", gap: "12px", flexDirection: "column", maxWidth: "300px" }}>
          <Button onClick={showClosableToast} variant="outline">
            Closable Toast
          </Button>
          <Button onClick={showNonClosableToast} variant="outline">
            Non-closable Toast
          </Button>
          <Button onClick={showClosableWithCustomClose} variant="outline">
            Closable + Custom Handler
          </Button>
        </div>
      )
    }

    return <IsClosableExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Demonstrates the isClosable property which controls whether the toast shows a close button. When isClosable is true, users can manually close the toast. When false, the toast can only be dismissed automatically via timeout.",
      },
    },
  },
}


// Real-world examples
export const RealWorldExamples: Story = {
  render: () => {
    const RealWorldExample = () => {
      const { add } = useToast()

      const simulateFileUpload = () => {
        add({
          title: "Uploading file...",
          description: "document.pdf",
          type: "info",
          startDecorator: <span>📄</span>,
        })

        setTimeout(() => {
          add({
            title: "Upload complete",
            description: "document.pdf has been uploaded successfully.",
            type: "success",
            startDecorator: <span>✅</span>,
            endDecorator: (
              <Button variant="outline" size="small">
                View
              </Button>
            ),
          })
        }, 2000)
      }

      const simulateFormValidation = () => {
        add({
          title: "Validation Error",
          description: "Please fill in all required fields before submitting.",
          type: "error",
          action: (
            <Button variant="outline" size="small">
              Review Form
            </Button>
          ),
        })
      }

      const simulateNetworkError = () => {
        add({
          title: "Connection Lost",
          description: "Unable to connect to server. Please check your internet connection.",
          type: "error",
          action: (
            <Button variant="solid" size="small">
              Retry
            </Button>
          ),
        })
      }

      const simulateSuccessfulSave = () => {
        add({
          title: "Changes saved",
          description: "Your changes have been saved automatically.",
          type: "success",
        })
      }

      return (
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "12px", maxWidth: "500px" }}>
          <Button onClick={simulateFileUpload} variant="outline">
            File Upload
          </Button>
          <Button onClick={simulateFormValidation} variant="outline">
            Form Validation
          </Button>
          <Button onClick={simulateNetworkError} variant="outline">
            Network Error
          </Button>
          <Button onClick={simulateSuccessfulSave} variant="outline">
            Auto Save
          </Button>
        </div>
      )
    }

    return <RealWorldExample />
  },
  parameters: {
    docs: {
      description: {
        story: "Real-world examples of toast usage in common application scenarios.",
      },
    },
  },
}