import { useState } from "react"
import {
  But<PERSON>,
  createThemeV2,
  Input,
  Modal,
  Theme,
  Typography,
} from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

const meta: Meta<typeof Modal.Root> = {
  title: "@apollo∕ui/Components/Feedback/Modal",
  component: Modal.Root,
  subcomponents: {
    ModalHeader: Modal.Header,
    ModalCloseButton: Modal.CloseButton,
    ModalContent: Modal.Content,
    ModalFooter: Modal.Footer,
  },
  decorators: [
    (Story) => (
      <Theme theme={createThemeV2()}>
        <Story />
      </Theme>
    ),
  ],
  tags: ["autodocs"],
  parameters: {
    controls: { expanded: true },
    docs: {
      description: {
        component:
          "The Basic Modal is intended for straightforward tasks, like selecting items or gathering basic information. Basic Modals help users focus on a single task without distractions. These modals do not support images or videos",
      },
      page: () => (
        <>
          <Title>Modal</Title>
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Modal } from "@apollo/ui"`} language="tsx" />
          <h3>Usage</h3>
          <h5>Modal components in Apollo Design System use a compositional pattern, consisting of multiple subcomponents that work together:</h5>
          <ol>
            <li>
              <code>Modal.Root</code>: The root component that wraps the entire
              modal.
            </li>
            <li>
              <code>Modal.Header</code>: The header section of the modal,
              typically containing a title and close button.
            </li>
            <li>
              <code>Modal.Content</code>: The main content area of the modal.
            </li>
            <li>
              <code>Modal.Footer</code>: The footer section, typically
              containing action buttons.
            </li>
            <li>
              <code>Modal.CloseButton</code>: A button that closes the modal when
              clicked.
            </li>
          </ol>
          <h3>Props</h3>
          <h4>Modal.Root</h4>
          <ArgTypes of={Modal.Root} />

          <h4> Modal.Header</h4>
          <ArgTypes of={Modal.Header} />

          <h4>Modal.CloseButton</h4>
          <ArgTypes of={Modal.CloseButton} />

          <h4>Modal.Content</h4>
          <ArgTypes of={Modal.Content} />

          <h4>Modal.Footer</h4>
          <ArgTypes of={Modal.Footer} />
          <Stories />
        </>
      ),
    },
  },
  argTypes: {
    // Modal.Root props
    open: {
      control: "boolean",
      description: "Controls whether the modal is open or closed",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "-" },
      },
    },
    onOpenChange: {
      action: "openChange",
      description: "Callback fired when the open state changes",
      table: {
        type: { summary: "(open: boolean) => void" },
      },
    },
    onOpenChangeComplete: {
      action: "openChangeComplete",
      description: "Callback fired when the open state change is completed",
      table: {
        type: { summary: "() => void" },
      },
    },
    defaultOpen: {
      control: "boolean",
      description: "The default open state of the modal",
      table: {
        type: { summary: "boolean" },
      },
    },
    dismissible: {
      control: "boolean",
      description: "If true, the modal can be dismissed",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "true" },
      },
    },
    className: {
      control: "text",
      description: "Additional CSS class names for the modal",
      table: {
        type: { summary: "string" },
      },
    },
    rootProps: {
      description: "Props passed to the root Dialog component",
      table: {
        type: { summary: "Dialog.Root.Props" },
      },
    },
    portalProps: {
      description: "Props passed to the Dialog Portal component",
      table: {
        type: { summary: "Dialog.Portal.Props" },
      },
    },
    backdropProps: {
      description: "Props passed to the Dialog Backdrop component",
      table: {
        type: { summary: "Dialog.Backdrop.Props" },
      },
    },
    popupProps: {
      description: "Props passed to the Dialog Popup component",
      table: {
        type: { summary: "Dialog.Popup.Props" },
      },
    },
    children: {
      description: "The content of the modal",
      table: {
        type: { summary: "ReactNode" },
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof Modal.Root>

export const Basic: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    return (
      <div>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here, you can custom it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setOpen(false)}>Confirm</Button>
          </Modal.Footer>
        </Modal.Root>
      </div>
    )
  },
}

export const HeaderWithIcon: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal With Icon</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header icon={<InfoCircle />}>
            <Typography level="titleMedium">Modal Title</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here, you can custom it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setOpen(false)}>Confirm</Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

export const FooterWithCustomButtons: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here, you can custom it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button color="danger" onClick={() => setOpen(false)}>
              Delete
            </Button>
            <Button onClick={() => setOpen(false)}>Accept</Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

export const WithoutCloseButton: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here, you can custom it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button onClick={() => setOpen(false)}>Ok</Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

export const CustomStyling: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    const customRootClassName = `.mypopup {background-color: red;}`
    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          className={customRootClassName}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <Typography level="bodyLarge">
              Modal content will appear here, you can custom it however you
              want, according to the user needs. Please make sure that the
              content is clear for completing the relevant task.
            </Typography>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button>Confirm</Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}

export const WithForm: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false)
    const [name, setName] = useState("")
    const [email, setEmail] = useState("")

    const handleSubmit = () => {
      console.log("Form submitted:", { name, email })
      setOpen(false)
    }
    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal.Root
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
          }}
          {...args}
        >
          <Modal.Header>
            <Typography level="titleMedium">Modal Title</Typography>
            <Modal.CloseButton />
          </Modal.Header>
          <Modal.Content>
            <div className="flex flex-col gap-4">
              <Typography level="bodySmall">
                Please fill in your information below:
              </Typography>
              <Input
                label="Name"
                placeholder="Enter your name"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
              <Input
                label="Email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
          </Modal.Content>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={!name || !email}>
              Confirm
            </Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    )
  },
}
