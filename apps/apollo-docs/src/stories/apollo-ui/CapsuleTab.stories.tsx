import { useState } from "react"
import { CapsuleTab, createThemeV2, Theme } from "@apollo/ui"
import type { Meta, StoryObj } from "@storybook/react"

// Example data
const tabs = [
  { id: "alpha", label: "Alpha" },
  { id: "beta", label: "Beta" },
  { id: "gamma", label: "Gamma" },
]

const meta: Meta<typeof CapsuleTab> = {
  title: "@apollo∕ui/Components/Layout/CapsuleTab",
  component: CapsuleTab,
  decorators: [
    (Story) => (
      <Theme theme={createThemeV2()}>
        <Story />
      </Theme>
    ),
  ],
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "The `CapsuleTab` component is used to create a tabbed interface with a capsule-style design. It allows users to switch between different views or content.",
      },
    },
  },
  argTypes: {
    tabs: {
      description: "Array of tab objects with `id` and `label`.",
      control: "object",
    },
    selectedIndex: {
      description: "Index of the currently selected tab.",
      control: "number",
    },
    onSelect: {
      description: "Callback when a tab is selected.",
      action: "selected",
    },
    className: {
      description: "Additional class names for the root element.",
      control: "text",
    },
  },
}
export default meta

type Story = StoryObj<typeof CapsuleTab>

export const Basic: Story = {
  render: (args) => {
    const [selectedIndex, setSelectedIndex] = useState(0)

    return (
      <CapsuleTab
        {...args}
        tabs={args.tabs ?? tabs}
        selectedIndex={selectedIndex}
        onSelect={setSelectedIndex}
      />
    )
  },
  args: {
    tabs,
    selectedIndex: 0,
  },
}