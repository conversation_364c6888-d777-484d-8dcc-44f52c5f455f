"use client"

import { But<PERSON>, useToast } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export function Toasts() {
  const { add } = useToast()
  return (
    <ComponentGroup>
      <ComponentBox>
        <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
              type: "success",
            })
          }
        >
          Success
        </Button>
       <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
              type: "error",
            })
          }
        >
          Error
        </Button>
        <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
              type: "warning",
            })
          }
        >
          Warning
        </Button>
        <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
            })
          }
        >
          Info
        </Button>
        <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
              onClose: () => {
                console.log("onClose")
              },
            })
          }
        >
          Info
        </Button>
        <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
              timeout: 1000,
            })
          }
        >
          Duration 1s
        </Button>
        <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
              timeout: 1000,
              fullWidth: true,
            })
          }
        >
          Fullwidth
        </Button>
        <Button
          onClick={() =>
            add({
              title: "Hello World",
              description: "This is a toast",
              timeout: 1000,
              width: "500px",
            })
          }
        >
          Width 500px
        </Button>
      </ComponentBox>
    </ComponentGroup>
  )
}
