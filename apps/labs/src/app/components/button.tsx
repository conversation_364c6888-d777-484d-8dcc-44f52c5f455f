import { <PERSON><PERSON> } from "@apollo/ui"
import { DeleteOutlined, <PERSON> } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

export const Buttons = () => (
  <>
    <ComponentGroup>
      <ComponentBox>
        <Button>Primary</Button>
        <Button startDecorator={<Smile size={16} />}>Primary</Button>
        <Button endDecorator={<Smile size={16} />}>Primary</Button>
        <Button endDecorator={<Smile size={16} />} disabled>
          Primary
        </Button>
        <Button disabled>Primary Disabled</Button>
      </ComponentBox>
      <ComponentBox>
        <Button variant="outline">Outline</Button>
        <Button startDecorator={<Smile size={16} />} variant="outline">
          Outline
        </Button>
        <Button endDecorator={<Smile size={16} />} variant="outline">
          Outline
        </Button>
        <Button endDecorator={<Smile size={16} />} variant="outline" disabled>
          Outline
        </Button>
        <Button variant="outline" disabled>
          Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button variant="plain">Plain</Button>
        <Button startDecorator={<Smile size={16} />} variant="plain">
          Plain
        </Button>
        <Button endDecorator={<Smile size={16} />} variant="plain">
          Plain
        </Button>
        <Button variant="plain" disabled>
          Plain Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button size="small">Small</Button>
        <Button startDecorator={<Smile size={14} />} size="small">
          Small
        </Button>
        <Button endDecorator={<Smile size={14} />} size="small">
          Small
        </Button>
        <Button size="small" disabled>
          Small Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button size="small" variant="outline">
          Small Outline
        </Button>
        <Button size="small" variant="outline" disabled>
          Small Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button size="small" variant="plain">
          Small Plain
        </Button>
        <Button size="small" variant="plain" disabled>
          Small Plain Disabled
        </Button>
      </ComponentBox>
      <Button fullWidth>Full Width</Button>
      <Button fullWidth size="small">
        Full Width Small
      </Button>
    </ComponentGroup>
    <ComponentGroup>
      <ComponentBox>
        <Button color="danger">Danger</Button>
        <Button startDecorator={<DeleteOutlined size={16} />} color="danger">
          Danger
        </Button>
        <Button endDecorator={<DeleteOutlined size={16} />} color="danger">
          Danger
        </Button>
        <Button color="danger" disabled>
          Danger Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" variant="outline">
          Danger Outline
        </Button>
        <Button
          startDecorator={<DeleteOutlined size={16} />}
          color="danger"
          variant="outline"
        >
          Danger Outline
        </Button>
        <Button
          endDecorator={<DeleteOutlined size={16} />}
          color="danger"
          variant="outline"
        >
          Danger Outline
        </Button>
        <Button color="danger" variant="outline" disabled>
          Danger Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" variant="plain">
          Danger Plain
        </Button>
        <Button color="danger" variant="plain" disabled>
          Danger Plain Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" size="small">
          Danger Small
        </Button>
        <Button color="danger" size="small" disabled>
          Danger Small Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" size="small" variant="outline">
          Danger Small Outline
        </Button>
        <Button color="danger" size="small" variant="outline" disabled>
          Danger Small Outline Disabled
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" size="small" variant="plain">
          Danger Small Plain
        </Button>
        <Button color="danger" size="small" variant="plain" disabled>
          Danger Small Plain Disabled
        </Button>
      </ComponentBox>
      <Button color="danger" fullWidth>
        Danger Full Width
      </Button>
      <Button color="danger" fullWidth size="small">
        Danger Full Width Small
      </Button>
    </ComponentGroup>
    <ComponentGroup>
      <ComponentBox>
        <Button startDecorator={<Smile size={16} />} href="https://google.com">
          Link Primary
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" variant="outline">
          Link Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" variant="plain">
          Link Plain
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" size="small">
          Link Small
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" size="small" variant="outline">
          Link Small Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button href="https://google.com" size="small" variant="plain">
          Link Small Plain
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" href="https://google.com">
          Link Danger
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" href="https://google.com" variant="outline">
          Link Danger Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" href="https://google.com" variant="plain">
          Link Danger Plain
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button color="danger" href="https://google.com" size="small">
          Link Danger Small
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button
          color="danger"
          href="https://google.com"
          size="small"
          variant="outline"
        >
          Link Danger Small Outline
        </Button>
      </ComponentBox>
      <ComponentBox>
        <Button
          color="danger"
          href="https://google.com"
          size="small"
          variant="plain"
        >
          Link Danger Small Plain
        </Button>
      </ComponentBox>
      <Button color="danger" fullWidth href="https://google.com">
        Link Danger Full Width
      </Button>
      <Button color="danger" fullWidth href="https://google.com" size="small">
        Link Danger Full Width Small
      </Button>
    </ComponentGroup>
  </>
)
