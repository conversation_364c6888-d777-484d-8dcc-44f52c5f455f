import React from "react"
import { I<PERSON><PERSON><PERSON><PERSON> } from "@apollo/ui"
import { <PERSON>, Smile } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

const IconButtons = () => {
  return (
    <ComponentGroup>
      <ComponentBox direction="horizontal">
        <IconButton>
          <Smile />
        </IconButton>
        <IconButton size="small">
          <Heart />
        </IconButton>
        <IconButton size="medium">
          <Heart />
        </IconButton>
        <IconButton
          size="large"
          onClick={() => {
            console.log("Click!")
          }}
        >
          <Heart />
        </IconButton>
        <IconButton disabled size="large">
          <Heart />
        </IconButton>
        <IconButton
          href="https://cjx-apollo-ui.netlify.app/docs/components/button"
          target="_blank"
          size="large"
        >
          <Heart />
        </IconButton>
      </ComponentBox>
      <ComponentBox direction="horizontal">
        <IconButton color="danger">
          <Smile />
        </IconButton>
        <IconButton color="danger" size="small">
          <Heart />
        </IconButton>
        <IconButton color="danger" size="medium">
          <Heart />
        </IconButton>
        <IconButton
          color="danger"
          size="large"
          onClick={() => {
            console.log("Click!")
          }}
          variant="outline"
        >
          <Heart />
        </IconButton>
        <IconButton color="danger" disabled size="large">
          <Heart />
        </IconButton>
        <IconButton
          color="danger"
          href="https://cjx-apollo-ui.netlify.app/docs/components/button"
          target="_blank"
          size="large"
          variant="solid"
        >
          <Heart />
        </IconButton>
      </ComponentBox>
    </ComponentGroup>
  )
}

export default IconButtons
