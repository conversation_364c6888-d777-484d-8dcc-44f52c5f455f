"use client"

import React from "react"
import { FloatButton } from "@apollo/ui"
import { Heart, Smile } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

const FloatButtons = () => {
  return (
    <ComponentGroup>
      <ComponentBox direction="horizontal">
        <ComponentBox direction="horizontal">
          <FloatButton icon={<Smile />} isExpanded label="Button" />
          <FloatButton
            iconSide="end"
            isExpanded
            icon={<Smile />}
            label="Button"
          />
          <FloatButton icon={<Smile />} isExpanded label="Button" disabled />
          <FloatButton icon={<Smile />} label="Button" />
          <FloatButton icon={<Heart />} label="Button" />
          <FloatButton icon={<Heart />} disabled label="Button" />
          <FloatButton color="danger" icon={<Heart />} label="Button" />
          <FloatButton color="danger" disabled icon={<Heart />} label="Button" />
        </ComponentBox>
      </ComponentBox>
    </ComponentGroup>
  )
}

export default FloatButtons
