import { Button } from "@apollo/ui"

export default function ButtonDemo() {
  return (
    <div style={styles}>
      <Button>Solid</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="plain">Plain</Button>
      <Button
        color="danger"
        variant="plain"
        href="https://cjx-apollo-ui.netlify.app/"
      >
        Link
      </Button>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  position: "fixed",
  width: "100%",
  height: "100%",
} as any
