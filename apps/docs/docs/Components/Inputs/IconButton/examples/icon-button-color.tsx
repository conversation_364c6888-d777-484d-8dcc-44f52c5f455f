import { IconButton } from "@apollo/ui"
import { <PERSON>, Smile } from "@design-systems/apollo-icons"

export default function IconButtonColorDemo() {
  return (
    <div style={styles}>
      <IconButton color="primary">
        <Smile />
      </IconButton>
      <IconButton color="danger">
        <Heart />
      </IconButton>
    </div>
  )
}

const styles = {
  display: "flex",
  gap: "16px",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  position: "fixed",
  width: "100%",
  height: "100%",
} as any
