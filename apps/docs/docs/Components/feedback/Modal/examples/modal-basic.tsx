import React, { useState } from "react"
import { Button, <PERSON>dal, Typography } from "@apollo/ui"

export default function ModalBasic() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button variant="solid" onClick={() => setOpen(true)}>
        Open Modal
      </Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[500px]"
      >
        <Modal.Header>
          <Typography level="h3">Basic Modal</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <Typography level="body1">
            A simple modal with default settings. This modal demonstrates the
            basic functionality with header, content, and footer sections.
          </Typography>
        </Modal.Content>
        <Modal.Footer>
          <Button
            className="self-stretch"
            variant="outline"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button
            className="self-stretch"
            variant="solid"
            onClick={() => setOpen(false)}
          >
            OK
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
