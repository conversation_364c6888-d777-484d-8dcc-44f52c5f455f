import React, { useState } from "react"
import { Button, Modal, Typography } from "@apollo/ui"

export default function ModalCustomButtons() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button variant="solid" onClick={() => setOpen(true)}>
        Open Modal with Custom Buttons
      </Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[500px]"
      >
        <Modal.Header>
          <Typography level="h3">Custom Buttons</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <Typography level="body1">
            This modal demonstrates how to add custom buttons in the footer
            section. You can customize the appearance and behavior of buttons as
            needed.
          </Typography>
        </Modal.Content>
        <Modal.Footer>
          <Button
            className="self-stretch"
            color="danger"
            onClick={() => setOpen(false)}
          >
            Delete
          </Button>
          <Button
            className="self-stretch"
            color="primary"
            onClick={() => setOpen(false)}
          >
            Save
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
