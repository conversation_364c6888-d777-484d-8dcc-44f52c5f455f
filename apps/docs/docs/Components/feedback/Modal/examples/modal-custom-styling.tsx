import React, { useState } from "react"
import { Button, Modal, Typography } from "@apollo/ui"

export default function ModalCustomStyling() {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button variant="solid" onClick={() => setOpen(true)}>
        Open Custom Styled Modal
      </Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[500px]"
      >
        <Modal.Header className="bg-primary-50 border-b border-primary-200">
          <Typography level="h3" className="text-primary-700">
            Custom Styled Modal
          </Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content className="bg-gray-50">
          <Typography level="body1">
            This example demonstrates how to apply custom styling to different
            parts of the modal. You can customize the header, content, and
            footer sections by applying CSS classes.
          </Typography>
        </Modal.Content>
        <Modal.Footer className="bg-primary-50 border-t border-primary-200">
          <Button
            className="self-stretch"
            variant="outline"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button
            className="self-stretch"
            variant="solid"
            color="primary"
            onClick={() => setOpen(false)}
          >
            OK
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
