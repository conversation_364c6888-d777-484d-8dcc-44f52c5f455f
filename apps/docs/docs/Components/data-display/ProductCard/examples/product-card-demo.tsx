import React from "react"
import { Button, ProductCard, Typography } from "@apollo/ui"

export default function ProductCardDemo() {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        gap: "16px",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        imageSrc="https://picsum.photos/200/300?random=1"
      />

      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น"
        imageSrc="https://picsum.photos/200/300?random=2"
        body={
          <Typography
            level="caption"
            style={{ color: "var(--color-content-danger-default)" }}
          >
            หมดอายุ 12/07/2567 (23:59)
          </Typography>
        }
        footer={
          <Button fullWidth variant="solid" color="primary">
            เก็บคูปอง
          </Button>
        }
      />
    </div>
  )
}
