import { mkdirSync, writeFileSync } from "fs"
import { dirname, join } from "path"
import { fileURLToPath } from "url"
import { ApolloToken } from "@apollo/token"

import { parseTokenV2 } from "../theme/utils"

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

function generateTokenVariableFile() {
  const tokens = parseTokenV2(ApolloToken)

  // Define the output path relative to the package root
  const packageRoot = join(__dirname, "../..")
  const outputPath = join(packageRoot, "src", "_variables.css")

  // Ensure the dist directory exists
  mkdirSync(dirname(outputPath), { recursive: true })

  // Write the CSS variables to the file with a header comment
  const cssContent = `/* This file is auto-generated. Do not edit manually. 
 * At ${new Date().toISOString()} 
*/
  \n:root {\n${tokens}\n}`
  writeFileSync(outputPath, cssContent, "utf8")

  console.log(`CSS variables file generated at: ${outputPath}`)
}

// Execute the function if this script is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateTokenVariableFile()
}
