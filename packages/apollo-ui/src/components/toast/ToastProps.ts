import { HTMLAttributes, PropsWithChildren } from "react"
import { Toast } from "@base-ui-components/react"

import { AlertProps } from "../alert"

export type TypeProp = "success" | "info" | "warning" | "error"

export type SwipeDirectionProp =
  | "left"
  | "right"
  | "up"
  | "down"
  | ("left" | "right" | "up" | "down")[]

export type PositionProp =
  | "top-center"
  | "top-right"
  | "top-left"
  | "bottom-center"
  | "bottom-right"
  | "bottom-left"


export type ToastObjectProps = Omit<
  Toast.Root.ToastObject,
  "type" | "actionProps"
> & {
  /** @default 'top-left' */
  position?: PositionProp
  type?: TypeProp
  isClosable?: boolean
  width?: string
  className?: string
} & Pick<AlertProps, "startDecorator" | "endDecorator" | "action" | "fullWidth">

export type ToastProps = Omit<Toast.Root.Props, "toast"> & {
  toast: ToastObjectProps
}

export type ToastWrapperProps = PropsWithChildren<{
  /** @default 'top-right' */
  position?: PositionProp
  toasts?: ToastObjectProps[]
}> & HTMLAttributes<HTMLDivElement>
