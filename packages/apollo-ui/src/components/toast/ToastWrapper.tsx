import { Toast as BaseToast } from "@base-ui-components/react/toast"
import { cva } from "class-variance-authority"
import classNames from "classnames"

import { Portal } from "../portal"
import { Toast } from "./Toast"
import styles from "./toast.module.css"
import { PositionProp, ToastWrapperProps } from "./ToastProps"

const toastWrapperVariants = cva(styles.toastWrapper, {
  variants: {
    position: {
      "top-center": styles.toastWrapperTopCenter,
      "top-right": styles.toastWrapperTopRight,
      "top-left": styles.toastWrapperTopLeft,
      "bottom-center": styles.toastWrapperBottomCenter,
      "bottom-right": styles.toastWrapperBottomRight,
      "bottom-left": styles.toastWrapperBottomLeft,
    },
  },
  defaultVariants: {
    position: "top-right",
  },
})

export function ToastWrapper({
  position = "top-right",
  toasts,
  children,
  ...divProps
}: ToastWrapperProps) {  
  return (
    <Portal baseComponent={<BaseToast.Portal />}>
      <BaseToast.Viewport
        {...divProps}
        className={classNames(
          `ApolloToast-wrapper`,
          toastWrapperVariants({ position: position as PositionProp }),
          divProps?.className
        )}
      >
        {toasts && toasts.length > 0
          ? toasts.map((toast) => {
              return <Toast key={toast.id} toast={toast} />
            })
          : null}
        {children}
      </BaseToast.Viewport>
    </Portal>
  )
}
